"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import {
  useGetContractorListingsQuery,
  useDeleteServiceListingMutation,
  ServiceListing,
} from "@/lib/store/api/serviceListingsApi";
import { ServiceListingForm } from "@/components/services/service-listing-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Briefcase,
  Plus,
  Filter,
  Search,
  ArrowLeft,
  Edit,
  Trash2,
  Eye,
  DollarSign,
  Star,
  Users,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

export default function ContractorServicesPage() {
  const router = useRouter();
  const { user, selectedRoles, isAuthenticated, profileCompletionStatus } =
    useSelector((state: RootState) => state.auth);
  const {
    data: listings = [],
    isLoading,
    error,
  } = useGetContractorListingsQuery();
  const [deleteServiceListing] = useDeleteServiceListingMutation();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingListing, setEditingListing] = useState<ServiceListing | null>(
    null
  );
  const [deletingListingId, setDeletingListingId] = useState<string | null>(
    null
  );
  const { toast } = useToast();

  // Check if user is a contractor and profile completion status
  const isContractor = selectedRoles?.includes("contractor");
  const contractorProfileComplete =
    profileCompletionStatus?.contractor?.completed || false;
  const contractorCompletionPercentage =
    profileCompletionStatus?.contractor?.percentage || 0;

  // Redirect non-contractors to role selection
  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!isContractor) {
      router.push("/auth/role-selection?redirect=/contractor/services");
      return;
    }
  }, [isAuthenticated, isContractor, router]);

  const activeListings = listings.filter(
    (listing) => listing.status === "active"
  );
  const draftListings = listings.filter(
    (listing) => listing.status === "draft"
  );
  const inactiveListings = listings.filter(
    (listing) => listing.status === "inactive"
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "draft":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "inactive":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4" />;
      case "draft":
        return <Clock className="h-4 w-4" />;
      case "inactive":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleDeleteListing = async (listingId: string) => {
    try {
      await deleteServiceListing(listingId).unwrap();

      toast({
        title: "Service Deleted",
        description: "Your service listing has been deleted successfully.",
      });

      setDeletingListingId(null);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete service listing.",
        variant: "destructive",
      });
    }
  };

  const ServiceListingCard = ({ listing }: { listing: ServiceListing }) => {
    return (
      <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg font-bold text-slate-900 dark:text-white mb-2">
                {listing.title}
              </CardTitle>
              <CardDescription className="text-slate-600 dark:text-slate-300">
                {listing.category}{" "}
                {listing.subcategory && `• ${listing.subcategory}`}
              </CardDescription>
            </div>
            <div className="flex flex-col gap-2">
              <Badge
                className={`${getStatusColor(
                  listing.status
                )} flex items-center gap-1`}
              >
                {getStatusIcon(listing.status)}
                {listing.status.charAt(0).toUpperCase() +
                  listing.status.slice(1)}
              </Badge>
              {listing.featured && (
                <Badge
                  variant="outline"
                  className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200"
                >
                  Featured
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Pricing */}
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-emerald-600" />
                <span className="font-medium text-slate-700 dark:text-slate-300">
                  {listing.price_type === "fixed"
                    ? "Fixed Price"
                    : listing.price_type === "hourly"
                    ? "Per Hour"
                    : listing.price_type === "per_sqft"
                    ? "Per Sq Ft"
                    : "Per Item"}
                </span>
              </div>
              <div className="text-right">
                <span className="text-xl font-bold text-emerald-600">
                  {formatCurrency(listing.base_price)}
                </span>
                {listing.min_price && listing.max_price && (
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Range: {formatCurrency(listing.min_price)} -{" "}
                    {formatCurrency(listing.max_price)}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="font-semibold">
                  {listing.rating.toFixed(1)}
                </span>
              </div>
              <p className="text-xs text-slate-600 dark:text-slate-400">
                Rating
              </p>
            </div>
            <div className="p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Users className="h-4 w-4 text-blue-500" />
                <span className="font-semibold">{listing.review_count}</span>
              </div>
              <p className="text-xs text-slate-600 dark:text-slate-400">
                Reviews
              </p>
            </div>
            <div className="p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <BarChart3 className="h-4 w-4 text-green-500" />
                <span className="font-semibold">{listing.order_count}</span>
              </div>
              <p className="text-xs text-slate-600 dark:text-slate-400">
                Orders
              </p>
            </div>
          </div>

          {/* Description */}
          {listing.description && (
            <p className="text-sm text-slate-600 dark:text-slate-300 line-clamp-2">
              {listing.description}
            </p>
          )}

          {/* Duration */}
          {listing.duration_estimate && (
            <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300">
              <Clock className="h-4 w-4" />
              <span>Duration: {listing.duration_estimate}</span>
            </div>
          )}

          {/* Tags */}
          {listing.tags && listing.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {listing.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {listing.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{listing.tags.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex flex-wrap gap-2 pt-4 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/contractor/services/${listing.id}`)}
            >
              <Eye className="h-4 w-4 mr-2" />
              View
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setEditingListing(listing)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>

            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Service Listing</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete "{listing.title}"? This
                    action cannot be undone.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button variant="outline">Cancel</Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleDeleteListing(listing.id)}
                  >
                    Delete Service
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Listing Info */}
          <div className="text-xs text-slate-500 dark:text-slate-400 pt-2 border-t">
            Created on {new Date(listing.created_at).toLocaleDateString()}
            {listing.updated_at !== listing.created_at && (
              <span>
                {" "}
                • Updated on {new Date(listing.updated_at).toLocaleDateString()}
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (!isAuthenticated || !isContractor) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/contractor/dashboard")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg">
                  <Briefcase className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                    My Services
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    Manage your service listings
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button
                onClick={() => setShowCreateForm(true)}
                disabled={!contractorProfileComplete}
                className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Service
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Completion Warning */}
        {!contractorProfileComplete && (
          <Card className="mb-8 border-orange-200 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <AlertCircle className="h-6 w-6 text-orange-600 mt-1" />
                <div className="flex-1">
                  <h3 className="font-semibold text-orange-800 dark:text-orange-200 mb-2">
                    Complete Your Profile to Create Services
                  </h3>
                  <p className="text-orange-700 dark:text-orange-300 mb-4">
                    Your profile is {contractorCompletionPercentage}% complete.
                    Complete it to start creating service listings.
                  </p>
                  <Button
                    onClick={() =>
                      router.push("/profile/complete?role=contractor")
                    }
                    className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  >
                    Complete Profile
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-100">Active Services</p>
                  <p className="text-3xl font-bold">{activeListings.length}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-emerald-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-yellow-500 to-orange-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100">Draft Services</p>
                  <p className="text-3xl font-bold">{draftListings.length}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Total Orders</p>
                  <p className="text-3xl font-bold">
                    {listings.reduce(
                      (sum, listing) => sum + listing.order_count,
                      0
                    )}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-pink-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Avg Rating</p>
                  <p className="text-3xl font-bold">
                    {listings.length > 0
                      ? (
                          listings.reduce(
                            (sum, listing) => sum + listing.rating,
                            0
                          ) / listings.length
                        ).toFixed(1)
                      : "0.0"}
                  </p>
                </div>
                <Star className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Services Tabs */}
        <Tabs defaultValue="active" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="active" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Active ({activeListings.length})
            </TabsTrigger>
            <TabsTrigger value="draft" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Draft ({draftListings.length})
            </TabsTrigger>
            <TabsTrigger value="inactive" className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              Inactive ({inactiveListings.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="space-y-6">
            {activeListings.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {activeListings.map((listing) => (
                  <ServiceListingCard key={listing.id} listing={listing} />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <CheckCircle className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">
                    No Active Services
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-6">
                    You don't have any active service listings. Create your
                    first service to start attracting customers.
                  </p>
                  <Button
                    onClick={() => setShowCreateForm(true)}
                    disabled={!contractorProfileComplete}
                    className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Service
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="draft" className="space-y-6">
            {draftListings.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {draftListings.map((listing) => (
                  <ServiceListingCard key={listing.id} listing={listing} />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <Clock className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">
                    No Draft Services
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    You don't have any draft service listings.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="inactive" className="space-y-6">
            {inactiveListings.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {inactiveListings.map((listing) => (
                  <ServiceListingCard key={listing.id} listing={listing} />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <AlertCircle className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">
                    No Inactive Services
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    You don't have any inactive service listings.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Service Form */}
      <ServiceListingForm
        open={showCreateForm}
        onOpenChange={setShowCreateForm}
        onSuccess={() => {
          setShowCreateForm(false);
          toast({
            title: "Service Created",
            description: "Your service listing has been created successfully.",
          });
        }}
        onCancel={() => setShowCreateForm(false)}
      />

      {/* Edit Service Form */}
      {editingListing && (
        <ServiceListingForm
          listing={editingListing}
          open={!!editingListing}
          onOpenChange={(open) => !open && setEditingListing(null)}
          onSuccess={() => {
            setEditingListing(null);
            toast({
              title: "Service Updated",
              description:
                "Your service listing has been updated successfully.",
            });
          }}
          onCancel={() => setEditingListing(null)}
        />
      )}
    </div>
  );
}
