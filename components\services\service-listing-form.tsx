"use client";

import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  CreateServiceListingRequest,
  useCreateServiceListingMutation,
  useUpdateServiceListingMutation,
  ServiceListing,
} from "@/lib/store/api/serviceListingsApi";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  FileText,
  DollarSign,
  MapPin,
  Plus,
  Trash2,
  Save,
  Eye,
  Upload,
  Tag,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

const additionalServiceSchema = z.object({
  name: z.string().min(1, "Service name is required"),
  price: z.number().min(0, "Price must be positive"),
  description: z.string().optional(),
});

const serviceListingSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().optional(),
  price_type: z.enum(["fixed", "hourly", "per_sqft", "per_item"]),
  base_price: z.number().min(0, "Base price must be positive"),
  min_price: z.number().min(0, "Min price must be positive").optional(),
  max_price: z.number().min(0, "Max price must be positive").optional(),
  duration_estimate: z.string().optional(),
  service_area: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  requirements: z.array(z.string()).optional(),
  included_services: z.array(z.string()).optional(),
  additional_services: z.array(additionalServiceSchema).optional(),
  status: z.enum(["active", "inactive", "draft"]).optional(),
});

type ServiceListingFormData = z.infer<typeof serviceListingSchema>;

interface ServiceListingFormProps {
  listing?: ServiceListing;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  onCancel: () => void;
}

const serviceCategories = [
  "Plumbing",
  "Electrical",
  "HVAC",
  "Carpentry",
  "Painting",
  "Roofing",
  "Flooring",
  "Landscaping",
  "Cleaning",
  "Moving",
  "Handyman",
  "Other",
];

const priceTypes = [
  { value: "fixed", label: "Fixed Price" },
  { value: "hourly", label: "Per Hour" },
  { value: "per_sqft", label: "Per Square Foot" },
  { value: "per_item", label: "Per Item" },
];

export function ServiceListingForm({
  listing,
  open,
  onOpenChange,
  onSuccess,
  onCancel,
}: ServiceListingFormProps) {
  const [createListing, { isLoading: isCreating }] = useCreateServiceListingMutation();
  const [updateListing, { isLoading: isUpdating }] = useUpdateServiceListingMutation();
  const [showPreview, setShowPreview] = useState(false);
  const [newTag, setNewTag] = useState("");
  const [newRequirement, setNewRequirement] = useState("");
  const [newIncludedService, setNewIncludedService] = useState("");
  const { toast } = useToast();

  const isEditing = !!listing;
  const isLoading = isCreating || isUpdating;

  const form = useForm<ServiceListingFormData>({
    resolver: zodResolver(serviceListingSchema),
    defaultValues: {
      title: listing?.title || "",
      description: listing?.description || "",
      category: listing?.category || "",
      subcategory: listing?.subcategory || "",
      price_type: listing?.price_type || "fixed",
      base_price: listing?.base_price || 0,
      min_price: listing?.min_price || undefined,
      max_price: listing?.max_price || undefined,
      duration_estimate: listing?.duration_estimate || "",
      service_area: listing?.service_area || [],
      tags: listing?.tags || [],
      requirements: listing?.requirements || [],
      included_services: listing?.included_services || [],
      additional_services: listing?.additional_services || [],
      status: listing?.status || "draft",
    },
  });

  const { fields: additionalServiceFields, append: appendAdditionalService, remove: removeAdditionalService } = useFieldArray({
    control: form.control,
    name: "additional_services",
  });

  const watchedPriceType = form.watch("price_type");
  const watchedTags = form.watch("tags") || [];
  const watchedRequirements = form.watch("requirements") || [];
  const watchedIncludedServices = form.watch("included_services") || [];

  const addTag = () => {
    if (newTag.trim() && !watchedTags.includes(newTag.trim())) {
      form.setValue("tags", [...watchedTags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    form.setValue("tags", watchedTags.filter(tag => tag !== tagToRemove));
  };

  const addRequirement = () => {
    if (newRequirement.trim() && !watchedRequirements.includes(newRequirement.trim())) {
      form.setValue("requirements", [...watchedRequirements, newRequirement.trim()]);
      setNewRequirement("");
    }
  };

  const removeRequirement = (requirementToRemove: string) => {
    form.setValue("requirements", watchedRequirements.filter(req => req !== requirementToRemove));
  };

  const addIncludedService = () => {
    if (newIncludedService.trim() && !watchedIncludedServices.includes(newIncludedService.trim())) {
      form.setValue("included_services", [...watchedIncludedServices, newIncludedService.trim()]);
      setNewIncludedService("");
    }
  };

  const removeIncludedService = (serviceToRemove: string) => {
    form.setValue("included_services", watchedIncludedServices.filter(service => service !== serviceToRemove));
  };

  const addAdditionalService = () => {
    appendAdditionalService({
      name: "",
      price: 0,
      description: "",
    });
  };

  const onSubmit = async (data: ServiceListingFormData) => {
    try {
      if (isEditing && listing) {
        await updateListing({
          id: listing.id,
          ...data,
        }).unwrap();

        toast({
          title: "Service Updated",
          description: "Your service listing has been updated successfully.",
        });
      } else {
        await createListing(data as CreateServiceListingRequest).unwrap();

        toast({
          title: "Service Created",
          description: "Your service listing has been created successfully.",
        });
      }

      onSuccess();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${isEditing ? 'update' : 'create'} service listing. Please try again.`,
        variant: "destructive",
      });
    }
  };

  const getPriceLabel = (priceType: string) => {
    switch (priceType) {
      case "hourly":
        return "per hour";
      case "per_sqft":
        return "per sq ft";
      case "per_item":
        return "per item";
      default:
        return "";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {isEditing ? "Edit Service Listing" : "Create Service Listing"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Update your service listing details."
              : "Create a new service listing to attract homeowners."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Provide basic details about your service
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Service Title *</Label>
                <Input
                  id="title"
                  placeholder="e.g., Professional Kitchen Renovation"
                  {...form.register("title")}
                />
                {form.formState.errors.title && (
                  <p className="text-sm text-red-600">
                    {form.formState.errors.title.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your service in detail..."
                  rows={4}
                  {...form.register("description")}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={form.watch("category")}
                    onValueChange={(value) => form.setValue("category", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {serviceCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.category && (
                    <p className="text-sm text-red-600">
                      {form.formState.errors.category.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subcategory">Subcategory</Label>
                  <Input
                    id="subcategory"
                    placeholder="e.g., Kitchen, Bathroom"
                    {...form.register("subcategory")}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Pricing
              </CardTitle>
              <CardDescription>
                Set your pricing structure
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="price_type">Price Type *</Label>
                <Select
                  value={form.watch("price_type")}
                  onValueChange={(value) => form.setValue("price_type", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select price type" />
                  </SelectTrigger>
                  <SelectContent>
                    {priceTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="base_price">
                    Base Price * {getPriceLabel(watchedPriceType) && `(${getPriceLabel(watchedPriceType)})`}
                  </Label>
                  <Input
                    id="base_price"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    {...form.register("base_price", { valueAsNumber: true })}
                  />
                  {form.formState.errors.base_price && (
                    <p className="text-sm text-red-600">
                      {form.formState.errors.base_price.message}
                    </p>
                  )}
                </div>

                {watchedPriceType !== "fixed" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="min_price">Min Price</Label>
                      <Input
                        id="min_price"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...form.register("min_price", { valueAsNumber: true })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="max_price">Max Price</Label>
                      <Input
                        id="max_price"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...form.register("max_price", { valueAsNumber: true })}
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration_estimate">Duration Estimate</Label>
                <Input
                  id="duration_estimate"
                  placeholder="e.g., 2-3 days, 4-6 hours"
                  {...form.register("duration_estimate")}
                />
              </div>
            </CardContent>
          </Card>

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                Tags & Keywords
              </CardTitle>
              <CardDescription>
                Add tags to help customers find your service
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Add a tag..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
                />
                <Button type="button" onClick={addTag} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {watchedTags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {watchedTags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:text-red-600"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <DialogFooter className="flex flex-col sm:flex-row gap-4 justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowPreview(true)}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Preview
            </Button>

            <div className="flex gap-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
              >
                <Save className="h-4 w-4 mr-2" />
                {isLoading 
                  ? (isEditing ? "Updating..." : "Creating...") 
                  : (isEditing ? "Update Service" : "Create Service")
                }
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
