import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '@/lib/supabase/client';

export interface ServiceListing {
  id: string;
  contractor_id: string;
  title: string;
  description?: string;
  category: string;
  subcategory?: string;
  price_type: 'fixed' | 'hourly' | 'per_sqft' | 'per_item';
  base_price: number;
  min_price?: number;
  max_price?: number;
  duration_estimate?: string;
  service_area?: string[];
  availability?: Record<string, any>;
  images?: string[];
  videos?: string[];
  tags?: string[];
  requirements?: string[];
  included_services?: string[];
  additional_services?: Array<{
    name: string;
    price: number;
    description?: string;
  }>;
  status: 'active' | 'inactive' | 'draft';
  featured: boolean;
  rating: number;
  review_count: number;
  order_count: number;
  created_at: string;
  updated_at: string;
}

export interface ServiceOrder {
  id: string;
  listing_id: string;
  homeowner_id: string;
  contractor_id: string;
  title: string;
  description?: string;
  total_amount: number;
  service_date?: string;
  service_time?: string;
  location_address: string;
  location_city?: string;
  location_state?: string;
  location_zip?: string;
  special_instructions?: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  payment_status: 'pending' | 'paid' | 'refunded';
  payment_method?: string;
  payment_intent_id?: string;
  created_at: string;
  updated_at: string;
}

export interface ServiceReview {
  id: string;
  listing_id: string;
  order_id: string;
  homeowner_id: string;
  contractor_id: string;
  rating: number;
  review_text?: string;
  images?: string[];
  response_text?: string;
  response_date?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateServiceListingRequest {
  title: string;
  description?: string;
  category: string;
  subcategory?: string;
  price_type: 'fixed' | 'hourly' | 'per_sqft' | 'per_item';
  base_price: number;
  min_price?: number;
  max_price?: number;
  duration_estimate?: string;
  service_area?: string[];
  availability?: Record<string, any>;
  images?: string[];
  videos?: string[];
  tags?: string[];
  requirements?: string[];
  included_services?: string[];
  additional_services?: Array<{
    name: string;
    price: number;
    description?: string;
  }>;
  status?: 'active' | 'inactive' | 'draft';
}

export interface CreateServiceOrderRequest {
  listing_id: string;
  contractor_id: string;
  title: string;
  description?: string;
  total_amount: number;
  service_date?: string;
  service_time?: string;
  location_address: string;
  location_city?: string;
  location_state?: string;
  location_zip?: string;
  special_instructions?: string;
}

export interface ServiceListingFilters {
  category?: string;
  subcategory?: string;
  price_min?: number;
  price_max?: number;
  rating_min?: number;
  location?: string;
  search?: string;
  sort_by?: 'rating' | 'price' | 'created_at' | 'order_count';
  sort_order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export const serviceListingsApi = createApi({
  reducerPath: 'serviceListingsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
  }),
  tagTypes: ['ServiceListing', 'ServiceOrder', 'ServiceReview'],
  endpoints: (builder) => ({
    // Get service listings with filters
    getServiceListings: builder.query<ServiceListing[], ServiceListingFilters>({
      queryFn: async (filters = {}) => {
        try {
          let query = supabase
            .from('service_listings')
            .select('*')
            .eq('status', 'active');

          // Apply filters
          if (filters.category) {
            query = query.eq('category', filters.category);
          }
          if (filters.subcategory) {
            query = query.eq('subcategory', filters.subcategory);
          }
          if (filters.price_min) {
            query = query.gte('base_price', filters.price_min);
          }
          if (filters.price_max) {
            query = query.lte('base_price', filters.price_max);
          }
          if (filters.rating_min) {
            query = query.gte('rating', filters.rating_min);
          }
          if (filters.search) {
            query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
          }

          // Apply sorting
          const sortBy = filters.sort_by || 'created_at';
          const sortOrder = filters.sort_order || 'desc';
          query = query.order(sortBy, { ascending: sortOrder === 'asc' });

          // Apply pagination
          if (filters.limit) {
            query = query.limit(filters.limit);
          }
          if (filters.offset) {
            query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1);
          }

          const { data, error } = await query;

          if (error) throw error;
          return { data: data || [] };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      providesTags: ['ServiceListing'],
    }),

    // Get single service listing
    getServiceListing: builder.query<ServiceListing, string>({
      queryFn: async (id) => {
        try {
          const { data, error } = await supabase
            .from('service_listings')
            .select('*')
            .eq('id', id)
            .single();

          if (error) throw error;
          return { data };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      providesTags: (result, error, id) => [{ type: 'ServiceListing', id }],
    }),

    // Get contractor's listings
    getContractorListings: builder.query<ServiceListing[], void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { data, error } = await supabase
            .from('service_listings')
            .select('*')
            .eq('contractor_id', user.id)
            .order('created_at', { ascending: false });

          if (error) throw error;
          return { data: data || [] };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      providesTags: ['ServiceListing'],
    }),

    // Create service listing
    createServiceListing: builder.mutation<ServiceListing, CreateServiceListingRequest>({
      queryFn: async (listingData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { data, error } = await supabase
            .from('service_listings')
            .insert({
              ...listingData,
              contractor_id: user.id,
            })
            .select()
            .single();

          if (error) throw error;
          return { data };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: ['ServiceListing'],
    }),

    // Update service listing
    updateServiceListing: builder.mutation<ServiceListing, Partial<ServiceListing> & { id: string }>({
      queryFn: async (listingData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { id, ...updateData } = listingData;

          const { data, error } = await supabase
            .from('service_listings')
            .update({
              ...updateData,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id)
            .eq('contractor_id', user.id) // Ensure only contractor can update
            .select()
            .single();

          if (error) throw error;
          return { data };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: (result, error, { id }) => [
        { type: 'ServiceListing', id },
        'ServiceListing',
      ],
    }),

    // Delete service listing
    deleteServiceListing: builder.mutation<void, string>({
      queryFn: async (id) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { error } = await supabase
            .from('service_listings')
            .delete()
            .eq('id', id)
            .eq('contractor_id', user.id); // Ensure only contractor can delete

          if (error) throw error;
          return { data: undefined };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: ['ServiceListing'],
    }),

    // Create service order
    createServiceOrder: builder.mutation<ServiceOrder, CreateServiceOrderRequest>({
      queryFn: async (orderData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { data, error } = await supabase
            .from('service_orders')
            .insert({
              ...orderData,
              homeowner_id: user.id,
            })
            .select()
            .single();

          if (error) throw error;
          return { data };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: ['ServiceOrder'],
    }),

    // Get service orders for user
    getServiceOrders: builder.query<ServiceOrder[], { role: 'homeowner' | 'contractor' }>({
      queryFn: async ({ role }) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const column = role === 'contractor' ? 'contractor_id' : 'homeowner_id';
          const { data, error } = await supabase
            .from('service_orders')
            .select('*')
            .eq(column, user.id)
            .order('created_at', { ascending: false });

          if (error) throw error;
          return { data: data || [] };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      providesTags: ['ServiceOrder'],
    }),

    // Update service order status
    updateServiceOrderStatus: builder.mutation<ServiceOrder, { id: string; status: ServiceOrder['status'] }>({
      queryFn: async ({ id, status }) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { data, error } = await supabase
            .from('service_orders')
            .update({
              status,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id)
            .eq('contractor_id', user.id) // Only contractor can update status
            .select()
            .single();

          if (error) throw error;
          return { data };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: (result, error, { id }) => [
        { type: 'ServiceOrder', id },
        'ServiceOrder',
      ],
    }),
  }),
});

export const {
  useGetServiceListingsQuery,
  useGetServiceListingQuery,
  useGetContractorListingsQuery,
  useCreateServiceListingMutation,
  useUpdateServiceListingMutation,
  useDeleteServiceListingMutation,
  useCreateServiceOrderMutation,
  useGetServiceOrdersQuery,
  useUpdateServiceOrderStatusMutation,
} = serviceListingsApi;
