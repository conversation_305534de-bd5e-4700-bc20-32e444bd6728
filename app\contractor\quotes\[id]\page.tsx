"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import {
  useGetQuoteQuery,
  useUpdateQuoteMutation,
  useDeleteQuoteMutation,
} from "@/lib/store/api/quotesApi";
import { QuoteDetailView } from "@/components/quotes/quote-detail-view";
import { QuoteEditForm } from "@/components/quotes/quote-edit-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  <PERSON>L<PERSON><PERSON>,
  FileText,
  Edit,
  Trash2,
  Eye,
  DollarSign,
  Calendar,
  User,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

export default function ContractorQuoteDetailPage() {
  const router = useRouter();
  const params = useParams();
  const quoteId = params.id as string;

  const { user, selectedRoles, isAuthenticated } = useSelector(
    (state: RootState) => state.auth
  );
  const { data: quote, isLoading, error } = useGetQuoteQuery(quoteId);
  const [updateQuote] = useUpdateQuoteMutation();
  const [deleteQuote] = useDeleteQuoteMutation();
  const [showDetailView, setShowDetailView] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!selectedRoles?.includes("contractor")) {
      router.push("/general-dashboard");
      return;
    }
  }, [isAuthenticated, selectedRoles, router]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "accepted":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      case "withdrawn":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "expired":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "accepted":
        return <CheckCircle className="h-4 w-4" />;
      case "rejected":
        return <XCircle className="h-4 w-4" />;
      case "withdrawn":
        return <AlertCircle className="h-4 w-4" />;
      case "expired":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleWithdrawQuote = async () => {
    try {
      await updateQuote({
        id: quoteId,
        status: "withdrawn",
      }).unwrap();

      toast({
        title: "Quote Withdrawn",
        description: "Your quote has been withdrawn successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to withdraw quote.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteQuote = async () => {
    try {
      await deleteQuote(quoteId).unwrap();

      toast({
        title: "Quote Deleted",
        description: "Your quote has been deleted successfully.",
      });

      router.push("/contractor/quotes");
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete quote.",
        variant: "destructive",
      });
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-20">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => router.push("/contractor/quotes")}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to Quotes
                </Button>
                <Skeleton className="h-8 w-48" />
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !quote) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-20">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => router.push("/contractor/quotes")}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to Quotes
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="text-center py-12">
            <CardContent>
              <XCircle className="h-16 w-16 mx-auto mb-4 text-red-400" />
              <h3 className="text-xl font-semibold mb-2">Quote Not Found</h3>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                The quote you're looking for doesn't exist or you don't have
                permission to view it.
              </p>
              <Button onClick={() => router.push("/contractor/quotes")}>
                Back to Quotes
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const isExpired =
    quote.valid_until && new Date(quote.valid_until) < new Date();
  const canEdit = quote.status === "pending" && !isExpired;
  const canDelete = quote.status === "rejected" || quote.status === "withdrawn";

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/contractor/quotes")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Quotes
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg">
                  <FileText className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                    {quote.title}
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    Quote Details
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Badge
                className={`${getStatusColor(
                  quote.status
                )} flex items-center gap-1`}
              >
                {getStatusIcon(quote.status)}
                {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
              </Badge>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDetailView(true)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>

                {canEdit && (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => setShowEditForm(true)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleWithdrawQuote}
                      className="text-orange-600 hover:bg-orange-50"
                    >
                      Withdraw
                    </Button>
                  </>
                )}

                {canDelete && (
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Delete Quote</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to delete this quote? This
                          action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter>
                        <Button variant="outline">Cancel</Button>
                        <Button
                          variant="destructive"
                          onClick={handleDeleteQuote}
                        >
                          Delete Quote
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quote Summary Card */}
        <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                  {quote.title}
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-300">
                  Project ID: {quote.project_id} • Quote ID: {quote.id}
                </CardDescription>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Quote Amount */}
            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 p-6 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <DollarSign className="h-6 w-6 text-emerald-600" />
                  <div>
                    <h3 className="font-semibold text-slate-700 dark:text-slate-300">
                      Total Quote Amount
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Labor: {formatCurrency(quote.labor_cost || 0)} •
                      Materials: {formatCurrency(quote.material_cost || 0)}
                    </p>
                  </div>
                </div>
                <span className="text-3xl font-bold text-emerald-600">
                  {formatCurrency(quote.total_amount)}
                </span>
              </div>
            </div>

            {/* Timeline */}
            {(quote.estimated_duration ||
              quote.start_date ||
              quote.end_date) && (
              <div className="flex items-center gap-6 p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <Calendar className="h-5 w-5 text-slate-600" />
                <div className="flex gap-6 text-sm">
                  {quote.estimated_duration && (
                    <div>
                      <span className="font-medium">Duration:</span>{" "}
                      {quote.estimated_duration} days
                    </div>
                  )}
                  {quote.start_date && (
                    <div>
                      <span className="font-medium">Start:</span>{" "}
                      {new Date(quote.start_date).toLocaleDateString()}
                    </div>
                  )}
                  {quote.end_date && (
                    <div>
                      <span className="font-medium">End:</span>{" "}
                      {new Date(quote.end_date).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Description */}
            {quote.description && (
              <div>
                <h4 className="font-semibold text-slate-900 dark:text-white mb-2">
                  Description
                </h4>
                <p className="text-slate-600 dark:text-slate-300">
                  {quote.description}
                </p>
              </div>
            )}

            {/* Notes */}
            {quote.notes && (
              <div>
                <h4 className="font-semibold text-slate-900 dark:text-white mb-2">
                  Notes
                </h4>
                <p className="text-slate-600 dark:text-slate-300">
                  {quote.notes}
                </p>
              </div>
            )}

            {/* Terms and Conditions */}
            {quote.terms_and_conditions && (
              <div>
                <h4 className="font-semibold text-slate-900 dark:text-white mb-2">
                  Terms & Conditions
                </h4>
                <p className="text-slate-600 dark:text-slate-300 text-sm">
                  {quote.terms_and_conditions}
                </p>
              </div>
            )}

            {/* Valid Until */}
            {quote.valid_until && (
              <div className="flex items-center gap-2 text-sm">
                <AlertCircle
                  className={`h-4 w-4 ${
                    isExpired ? "text-red-500" : "text-orange-500"
                  }`}
                />
                <span
                  className={isExpired ? "text-red-600" : "text-orange-600"}
                >
                  {isExpired ? "Expired on" : "Valid until"}:{" "}
                  {new Date(quote.valid_until).toLocaleDateString()}
                </span>
              </div>
            )}

            {/* Quote Info */}
            <div className="text-xs text-slate-500 dark:text-slate-400 pt-4 border-t">
              Submitted on {new Date(quote.created_at).toLocaleDateString()} at{" "}
              {new Date(quote.created_at).toLocaleTimeString()}
              {quote.updated_at !== quote.created_at && (
                <span>
                  {" "}
                  • Last updated on{" "}
                  {new Date(quote.updated_at).toLocaleDateString()}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quote Detail View Dialog */}
      <QuoteDetailView
        quoteId={quoteId}
        open={showDetailView}
        onOpenChange={setShowDetailView}
        userRole="contractor"
      />

      {/* Quote Edit Form Dialog */}
      <QuoteEditForm
        quoteId={quoteId}
        open={showEditForm}
        onOpenChange={setShowEditForm}
        onSuccess={() => {
          setShowEditForm(false);
          toast({
            title: "Quote Updated",
            description: "Your quote has been updated successfully.",
          });
        }}
        onCancel={() => setShowEditForm(false)}
      />
    </div>
  );
}
