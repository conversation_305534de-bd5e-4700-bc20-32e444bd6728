"use client";

import React, { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Quote,
  QuoteItem,
  useUpdateQuoteMutation,
  useGetQuoteQuery,
} from "@/lib/store/api/quotesApi";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  FileText,
  DollarSign,
  Calendar,
  Plus,
  Trash2,
  Calculator,
  Save,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

const quoteItemSchema = z.object({
  description: z.string().min(1, "Description is required"),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  unit_price: z.number().min(0, "Unit price must be positive"),
  item_type: z.enum(["labor", "material", "equipment", "other"]),
});

const quoteEditSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  labor_cost: z.number().min(0, "Labor cost must be positive"),
  material_cost: z.number().min(0, "Material cost must be positive"),
  estimated_duration: z.number().min(1, "Duration must be at least 1 day"),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  notes: z.string().optional(),
  terms_and_conditions: z.string().optional(),
  valid_until: z.string().optional(),
  items: z.array(quoteItemSchema).optional(),
});

type QuoteEditFormData = z.infer<typeof quoteEditSchema>;

interface QuoteEditFormProps {
  quoteId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  onCancel: () => void;
}

export function QuoteEditForm({
  quoteId,
  open,
  onOpenChange,
  onSuccess,
  onCancel,
}: QuoteEditFormProps) {
  const { data: quote, isLoading: isLoadingQuote } = useGetQuoteQuery(quoteId);
  const [updateQuote, { isLoading }] = useUpdateQuoteMutation();
  const [showPreview, setShowPreview] = useState(false);
  const { toast } = useToast();

  const form = useForm<QuoteEditFormData>({
    resolver: zodResolver(quoteEditSchema),
    defaultValues: {
      title: "",
      description: "",
      labor_cost: 0,
      material_cost: 0,
      estimated_duration: 1,
      start_date: "",
      end_date: "",
      notes: "",
      terms_and_conditions: "",
      valid_until: "",
      items: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Load quote data when available
  useEffect(() => {
    if (quote) {
      form.reset({
        title: quote.title,
        description: quote.description || "",
        labor_cost: quote.labor_cost || 0,
        material_cost: quote.material_cost || 0,
        estimated_duration: quote.estimated_duration || 1,
        start_date: quote.start_date || "",
        end_date: quote.end_date || "",
        notes: quote.notes || "",
        terms_and_conditions: quote.terms_and_conditions || "",
        valid_until: quote.valid_until || "",
        items: quote.items || [],
      });
    }
  }, [quote, form]);

  const watchedItems = form.watch("items") || [];
  const watchedLaborCost = form.watch("labor_cost") || 0;
  const watchedMaterialCost = form.watch("material_cost") || 0;

  const itemsTotal = watchedItems.reduce(
    (sum, item) => sum + (item.quantity * item.unit_price),
    0
  );
  const totalAmount = watchedLaborCost + watchedMaterialCost + itemsTotal;

  const addItem = () => {
    append({
      description: "",
      quantity: 1,
      unit_price: 0,
      item_type: "material",
    });
  };

  const onSubmit = async (data: QuoteEditFormData) => {
    try {
      await updateQuote({
        id: quoteId,
        ...data,
        total_amount: totalAmount,
      }).unwrap();

      toast({
        title: "Quote Updated",
        description: "Your quote has been updated successfully.",
      });

      onSuccess();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update quote. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoadingQuote) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Quote</DialogTitle>
            <DialogDescription>Loading quote data...</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="h-32 bg-slate-200 animate-pulse rounded"></div>
            <div className="h-20 bg-slate-200 animate-pulse rounded"></div>
            <div className="h-16 bg-slate-200 animate-pulse rounded"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!quote) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Edit Quote
          </DialogTitle>
          <DialogDescription>
            Update your quote details. Changes will be saved immediately.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Quote Information
              </CardTitle>
              <CardDescription>
                Update basic information about your quote
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Quote Title *</Label>
                <Input
                  id="title"
                  placeholder="e.g., Kitchen Renovation Quote"
                  {...form.register("title")}
                />
                {form.formState.errors.title && (
                  <p className="text-sm text-red-600">
                    {form.formState.errors.title.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the work to be performed..."
                  rows={3}
                  {...form.register("description")}
                />
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Pricing
              </CardTitle>
              <CardDescription>
                Set your labor and material costs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="labor_cost">Labor Cost</Label>
                  <Input
                    id="labor_cost"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    {...form.register("labor_cost", { valueAsNumber: true })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="material_cost">Material Cost</Label>
                  <Input
                    id="material_cost"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    {...form.register("material_cost", { valueAsNumber: true })}
                  />
                </div>
              </div>

              {/* Total Display */}
              <div className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-slate-700 dark:text-slate-300">
                    Total Quote Amount
                  </span>
                  <span className="text-2xl font-bold text-emerald-600">
                    {formatCurrency(totalAmount)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Timeline
              </CardTitle>
              <CardDescription>
                Set project timeline and duration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="estimated_duration">Duration (days)</Label>
                  <Input
                    id="estimated_duration"
                    type="number"
                    min="1"
                    placeholder="7"
                    {...form.register("estimated_duration", { valueAsNumber: true })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="start_date">Start Date</Label>
                  <Input
                    id="start_date"
                    type="date"
                    {...form.register("start_date")}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_date">End Date</Label>
                  <Input
                    id="end_date"
                    type="date"
                    {...form.register("end_date")}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <DialogFooter className="flex flex-col sm:flex-row gap-4 justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowPreview(true)}
              className="flex items-center gap-2"
            >
              <Calculator className="h-4 w-4" />
              Preview Changes
            </Button>

            <div className="flex gap-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || totalAmount <= 0}
                className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
              >
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
