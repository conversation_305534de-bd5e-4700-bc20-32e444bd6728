"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Quote,
  QuoteItem,
  useUpdateQuoteStatusMutation,
} from "@/lib/store/api/quotesApi";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Calendar,
  FileText,
  User,
  AlertCircle,
  Eye,
  MessageSquare,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

interface QuoteReviewCardProps {
  quote: Quote & { items?: QuoteItem[] };
  contractorName?: string;
  projectTitle?: string;
}

export function QuoteReviewCard({
  quote,
  contractorName,
  projectTitle,
}: QuoteReviewCardProps) {
  const router = useRouter();
  const [updateQuoteStatus, { isLoading }] = useUpdateQuoteStatusMutation();
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showAcceptDialog, setShowAcceptDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const { toast } = useToast();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "accepted":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      case "withdrawn":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "expired":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "accepted":
        return <CheckCircle className="h-4 w-4" />;
      case "rejected":
        return <XCircle className="h-4 w-4" />;
      case "withdrawn":
        return <AlertCircle className="h-4 w-4" />;
      case "expired":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleAcceptQuote = async () => {
    try {
      await updateQuoteStatus({
        quote_id: quote.id,
        status: "accepted",
      }).unwrap();

      toast({
        title: "Quote Accepted",
        description:
          "You have successfully accepted this quote. The contractor will be notified.",
      });
      setShowAcceptDialog(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description:
          error.message || "Failed to accept quote. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRejectQuote = async () => {
    try {
      await updateQuoteStatus({
        quote_id: quote.id,
        status: "rejected",
        reason: rejectReason,
      }).unwrap();

      toast({
        title: "Quote Rejected",
        description:
          "You have rejected this quote. The contractor will be notified.",
      });
      setShowRejectDialog(false);
      setRejectReason("");
    } catch (error: any) {
      toast({
        title: "Error",
        description:
          error.message || "Failed to reject quote. Please try again.",
        variant: "destructive",
      });
    }
  };

  const isExpired =
    quote.valid_until && new Date(quote.valid_until) < new Date();
  const canTakeAction = quote.status === "pending" && !isExpired;

  return (
    <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-xl font-bold text-slate-900 dark:text-white mb-2">
              {quote.title}
            </CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-300">
              {projectTitle && `Project: ${projectTitle}`}
              {contractorName && ` • Contractor: ${contractorName}`}
            </CardDescription>
          </div>
          <Badge
            className={`${getStatusColor(
              quote.status
            )} flex items-center gap-1`}
          >
            {getStatusIcon(quote.status)}
            {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Quote Amount */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-slate-700 dark:text-slate-300">
                Total Amount
              </span>
            </div>
            <span className="text-2xl font-bold text-blue-600">
              {formatCurrency(quote.total_amount)}
            </span>
          </div>

          {(quote.labor_cost || quote.material_cost) && (
            <div className="mt-3 grid grid-cols-2 gap-4 text-sm">
              {quote.labor_cost && (
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">
                    Labor:
                  </span>
                  <span className="font-medium">
                    {formatCurrency(quote.labor_cost)}
                  </span>
                </div>
              )}
              {quote.material_cost && (
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">
                    Materials:
                  </span>
                  <span className="font-medium">
                    {formatCurrency(quote.material_cost)}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Timeline */}
        {(quote.estimated_duration || quote.start_date || quote.end_date) && (
          <div className="flex items-center gap-4 p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
            <Calendar className="h-5 w-5 text-slate-600" />
            <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              {quote.estimated_duration && (
                <div>
                  <span className="text-slate-600 dark:text-slate-400">
                    Duration:
                  </span>
                  <p className="font-medium">{quote.estimated_duration} days</p>
                </div>
              )}
              {quote.start_date && (
                <div>
                  <span className="text-slate-600 dark:text-slate-400">
                    Start Date:
                  </span>
                  <p className="font-medium">
                    {new Date(quote.start_date).toLocaleDateString()}
                  </p>
                </div>
              )}
              {quote.end_date && (
                <div>
                  <span className="text-slate-600 dark:text-slate-400">
                    End Date:
                  </span>
                  <p className="font-medium">
                    {new Date(quote.end_date).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Description */}
        {quote.description && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-slate-600" />
              <Label className="text-sm font-medium">Description</Label>
            </div>
            <p className="text-sm text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700 p-3 rounded-lg">
              {quote.description}
            </p>
          </div>
        )}

        {/* Quote Items */}
        {quote.items && quote.items.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-slate-600" />
              <Label className="text-sm font-medium">Quote Breakdown</Label>
            </div>
            <div className="space-y-2">
              {quote.items.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg"
                >
                  <div className="flex-1">
                    <p className="font-medium text-sm">{item.description}</p>
                    <p className="text-xs text-slate-600 dark:text-slate-400">
                      {item.quantity} × {formatCurrency(item.unit_price)} (
                      {item.item_type})
                    </p>
                  </div>
                  <span className="font-medium">
                    {formatCurrency(item.total_price)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Terms and Conditions */}
        {quote.terms_and_conditions && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-slate-600" />
              <Label className="text-sm font-medium">Terms & Conditions</Label>
            </div>
            <p className="text-sm text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700 p-3 rounded-lg">
              {quote.terms_and_conditions}
            </p>
          </div>
        )}

        {/* Valid Until */}
        {quote.valid_until && (
          <div className="flex items-center gap-2 text-sm">
            <AlertCircle
              className={`h-4 w-4 ${
                isExpired ? "text-red-500" : "text-orange-500"
              }`}
            />
            <span className={isExpired ? "text-red-600" : "text-orange-600"}>
              {isExpired ? "Quote expired on" : "Valid until"}:{" "}
              {new Date(quote.valid_until).toLocaleDateString()}
            </span>
          </div>
        )}

        {/* View Details Button */}
        <div className="pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => router.push(`/homeowner/quotes/${quote.id}`)}
            className="w-full sm:w-auto"
          >
            <Eye className="h-4 w-4 mr-2" />
            View Full Details
          </Button>
        </div>

        {/* Action Buttons */}
        {canTakeAction && (
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
            <Dialog open={showAcceptDialog} onOpenChange={setShowAcceptDialog}>
              <DialogTrigger asChild>
                <Button className="flex-1 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Accept Quote
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Accept Quote</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to accept this quote for{" "}
                    {formatCurrency(quote.total_amount)}? This will notify the
                    contractor and you can proceed with the project.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowAcceptDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAcceptQuote}
                    disabled={isLoading}
                    className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
                  >
                    {isLoading ? "Accepting..." : "Accept Quote"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="flex-1 border-red-200 text-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Quote
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Reject Quote</DialogTitle>
                  <DialogDescription>
                    Please provide a reason for rejecting this quote. This will
                    help the contractor understand your decision.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="reason">
                      Reason for rejection (optional)
                    </Label>
                    <Textarea
                      id="reason"
                      placeholder="e.g., Price too high, timeline doesn't work, found another contractor..."
                      value={rejectReason}
                      onChange={(e) => setRejectReason(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowRejectDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleRejectQuote}
                    disabled={isLoading}
                    variant="destructive"
                  >
                    {isLoading ? "Rejecting..." : "Reject Quote"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}

        {/* Quote Info */}
        <div className="text-xs text-slate-500 dark:text-slate-400 pt-2 border-t">
          Quote submitted on {new Date(quote.created_at).toLocaleDateString()}{" "}
          at {new Date(quote.created_at).toLocaleTimeString()}
        </div>
      </CardContent>
    </Card>
  );
}
