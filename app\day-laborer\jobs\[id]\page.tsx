"use client";

import { useEffect, useState } from "react";
import { useRouter, use<PERSON>ara<PERSON> } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ArrowLeft,
  MapPin,
  Clock,
  DollarSign,
  Calendar,
  Users,
  Star,
  CheckCircle,
  AlertCircle,
  Briefcase,
  User,
  Phone,
  Mail,
  MessageSquare,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

// Mock job data - in real app, this would come from API
const mockJobData = {
  id: "1",
  title: "Moving Help - 2 Bedroom Apartment",
  description: "Need help moving furniture and boxes from a 2-bedroom apartment to a new house. This includes heavy lifting of furniture such as sofas, beds, dressers, and dining table. We also have about 30 boxes of various sizes that need to be carefully transported. The move is local within San Francisco, approximately 5 miles between locations. We will provide moving truck and basic equipment like dollies and straps. Looking for reliable, careful workers who can handle heavy items without damage.",
  client: {
    name: "<PERSON> <PERSON>",
    rating: 4.8,
    reviewsCount: 23,
    verified: true,
    memberSince: "2022-03-15",
    completedJobs: 45,
    phone: "+****************",
    email: "<EMAIL>",
  },
  hourlyRate: 25,
  duration: 6,
  date: "2024-01-25",
  startTime: "09:00",
  endTime: "15:00",
  location: "Downtown, San Francisco",
  fullAddress: "123 Market Street, San Francisco, CA 94102",
  category: "Moving",
  requirements: [
    "Physical strength for heavy lifting",
    "Reliable and punctual",
    "Own transportation to job site",
    "Experience with furniture moving preferred",
    "Must be able to lift 50+ lbs",
  ],
  responsibilities: [
    "Pack and wrap furniture and fragile items",
    "Load and unload moving truck",
    "Carry boxes and furniture up/down stairs",
    "Assemble/disassemble furniture as needed",
    "Ensure all items are handled with care",
  ],
  urgency: "medium",
  status: "available",
  postedDate: "2024-01-20",
  applicants: 3,
  totalEarnings: 150, // hourlyRate * duration
  equipment: ["Moving truck", "Dollies", "Straps", "Blankets"],
  additionalInfo: "Parking is available on-site. Lunch will be provided. Please bring work gloves and wear comfortable, sturdy shoes.",
};

export default function DayLaborerJobDetailPage() {
  const router = useRouter();
  const params = useParams();
  const jobId = params.id as string;
  
  const { user, selectedRoles, isAuthenticated, profileCompletionStatus } = useSelector((state: RootState) => state.auth);
  const [job, setJob] = useState(mockJobData);
  const [isLoading, setIsLoading] = useState(false);
  const [hasApplied, setHasApplied] = useState(false);

  // Check if user is a day laborer and profile completion status
  const isDayLaborer = selectedRoles?.includes("day_laborer");
  const dayLaborerProfileComplete = profileCompletionStatus?.day_laborer?.completed || false;

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!isDayLaborer) {
      router.push("/auth/role-selection?redirect=/day-laborer/jobs");
      return;
    }

    // In real app, fetch job data here
    // fetchJobData(jobId);
  }, [isAuthenticated, isDayLaborer, router, jobId]);

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const handleApplyToJob = () => {
    if (!dayLaborerProfileComplete) {
      router.push("/profile/complete?role=day_laborer");
      return;
    }

    // In real app, submit application to API
    setHasApplied(true);
    setJob(prev => ({ ...prev, applicants: prev.applicants + 1 }));
  };

  const handleContactClient = () => {
    // In real app, this would open messaging interface
    console.log("Contact client");
  };

  if (!isAuthenticated || !isDayLaborer) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-orange-200/50 dark:border-slate-700/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-20">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => router.push("/day-laborer/jobs")}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back to Jobs
                </Button>
                <Skeleton className="h-8 w-48" />
              </div>
            </div>
          </div>
        </div>
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-orange-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/day-laborer/jobs")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Jobs
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-lg">
                  <Briefcase className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">{job.title}</h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">Job Details</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Badge className={`${getUrgencyColor(job.urgency)} flex items-center gap-1`}>
                {job.urgency.charAt(0).toUpperCase() + job.urgency.slice(1)} Priority
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Job Overview */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white">
                  {job.title}
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-300">
                  Posted on {new Date(job.postedDate).toLocaleDateString()}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Key Details */}
                <div className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-6 rounded-lg">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="flex items-center gap-3">
                      <DollarSign className="h-6 w-6 text-orange-600" />
                      <div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">Hourly Rate</p>
                        <p className="text-xl font-bold text-orange-600">{formatCurrency(job.hourlyRate)}/hour</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Clock className="h-6 w-6 text-orange-600" />
                      <div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">Duration</p>
                        <p className="text-xl font-bold text-slate-900 dark:text-white">{job.duration} hours</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="h-6 w-6 text-orange-600" />
                      <div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">Date & Time</p>
                        <p className="font-semibold text-slate-900 dark:text-white">
                          {new Date(job.date).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {job.startTime} - {job.endTime}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <MapPin className="h-6 w-6 text-orange-600" />
                      <div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">Location</p>
                        <p className="font-semibold text-slate-900 dark:text-white">{job.location}</p>
                      </div>
                    </div>
                  </div>

                  {/* Total Earnings */}
                  <div className="mt-6 pt-6 border-t border-orange-200 dark:border-orange-800">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-medium text-slate-700 dark:text-slate-300">
                        Total Potential Earnings
                      </span>
                      <span className="text-2xl font-bold text-orange-600">
                        {formatCurrency(job.totalEarnings)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Job Description</h3>
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed">{job.description}</p>
                </div>

                {/* Responsibilities */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Responsibilities</h3>
                  <ul className="space-y-2">
                    {job.responsibilities.map((responsibility, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-slate-600 dark:text-slate-300">{responsibility}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Requirements */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Requirements</h3>
                  <div className="space-y-2">
                    {job.requirements.map((requirement, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                        <span className="text-slate-600 dark:text-slate-300">{requirement}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Equipment Provided */}
                {job.equipment.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Equipment Provided</h3>
                    <div className="flex flex-wrap gap-2">
                      {job.equipment.map((item, index) => (
                        <Badge key={index} variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Additional Information */}
                {job.additionalInfo && (
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Additional Information</h3>
                    <p className="text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                      {job.additionalInfo}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Application Status */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Application</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {!dayLaborerProfileComplete ? (
                  <div className="text-center">
                    <AlertCircle className="h-12 w-12 mx-auto mb-3 text-orange-600" />
                    <h4 className="font-semibold text-slate-900 dark:text-white mb-2">
                      Complete Your Profile
                    </h4>
                    <p className="text-sm text-slate-600 dark:text-slate-300 mb-4">
                      Complete your day laborer profile to apply for this job.
                    </p>
                    <Button 
                      onClick={() => router.push("/profile/complete?role=day_laborer")}
                      className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                    >
                      Complete Profile
                    </Button>
                  </div>
                ) : hasApplied ? (
                  <div className="text-center">
                    <CheckCircle className="h-12 w-12 mx-auto mb-3 text-green-600" />
                    <h4 className="font-semibold text-slate-900 dark:text-white mb-2">
                      Application Submitted
                    </h4>
                    <p className="text-sm text-slate-600 dark:text-slate-300 mb-4">
                      Your application has been submitted. The client will review and contact you if selected.
                    </p>
                    <Button 
                      variant="outline"
                      onClick={handleContactClient}
                      className="w-full"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Message Client
                    </Button>
                  </div>
                ) : (
                  <div className="text-center">
                    <Briefcase className="h-12 w-12 mx-auto mb-3 text-orange-600" />
                    <h4 className="font-semibold text-slate-900 dark:text-white mb-2">
                      Apply for This Job
                    </h4>
                    <p className="text-sm text-slate-600 dark:text-slate-300 mb-4">
                      Submit your application to be considered for this position.
                    </p>
                    <Button 
                      onClick={handleApplyToJob}
                      className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                    >
                      Apply Now
                    </Button>
                  </div>
                )}

                {/* Application Stats */}
                <div className="pt-4 border-t">
                  <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300">
                    <Users className="h-4 w-4" />
                    <span>{job.applicants} people have applied</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Client Information */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Client Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-600 to-red-600 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-slate-900 dark:text-white">{job.client.name}</h4>
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm">{job.client.rating}</span>
                      <span className="text-sm text-slate-500">({job.client.reviewsCount} reviews)</span>
                    </div>
                  </div>
                  {job.client.verified && (
                    <Badge variant="outline" className="text-xs">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  )}
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">Member since:</span>
                    <span className="font-medium">{new Date(job.client.memberSince).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">Jobs completed:</span>
                    <span className="font-medium">{job.client.completedJobs}</span>
                  </div>
                </div>

                {hasApplied && (
                  <div className="pt-4 border-t space-y-2">
                    <Button variant="outline" size="sm" className="w-full">
                      <Phone className="h-4 w-4 mr-2" />
                      {job.client.phone}
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      <Mail className="h-4 w-4 mr-2" />
                      {job.client.email}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Location */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <MapPin className="h-5 w-5 text-orange-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-slate-900 dark:text-white">{job.location}</p>
                      <p className="text-sm text-slate-600 dark:text-slate-300">{job.fullAddress}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
