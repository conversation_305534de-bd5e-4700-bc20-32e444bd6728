"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import {
  useGetQuotesQuery,
  useUpdateQuoteMutation,
  useDeleteQuoteMutation,
} from "@/lib/store/api/quotesApi";
import { QuoteCreationForm } from "@/components/quotes/quote-creation-form";
import { QuoteEditForm } from "@/components/quotes/quote-edit-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Briefcase,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Filter,
  Search,
  ArrowLeft,
  Edit,
  Trash2,
  Eye,
  DollarSign,
  Calendar,
  User,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

export default function ContractorQuotesPage() {
  const router = useRouter();
  const { user, selectedRoles, isAuthenticated } = useSelector(
    (state: RootState) => state.auth
  );
  const {
    data: quotes = [],
    isLoading,
    error,
  } = useGetQuotesQuery({ role: "contractor" });
  const [updateQuote] = useUpdateQuoteMutation();
  const [deleteQuote] = useDeleteQuoteMutation();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedQuote, setSelectedQuote] = useState<any>(null);
  const [editingQuoteId, setEditingQuoteId] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!selectedRoles?.includes("contractor")) {
      router.push("/general-dashboard");
      return;
    }
  }, [isAuthenticated, selectedRoles, router]);

  const pendingQuotes = quotes.filter((q) => q.status === "pending");
  const acceptedQuotes = quotes.filter((q) => q.status === "accepted");
  const rejectedQuotes = quotes.filter((q) => q.status === "rejected");
  const withdrawnQuotes = quotes.filter((q) => q.status === "withdrawn");

  const getStatusCount = (status: string) => {
    switch (status) {
      case "pending":
        return pendingQuotes.length;
      case "accepted":
        return acceptedQuotes.length;
      case "rejected":
        return rejectedQuotes.length;
      case "withdrawn":
        return withdrawnQuotes.length;
      default:
        return 0;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "accepted":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      case "withdrawn":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "expired":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "accepted":
        return <CheckCircle className="h-4 w-4" />;
      case "rejected":
        return <XCircle className="h-4 w-4" />;
      case "withdrawn":
        return <AlertCircle className="h-4 w-4" />;
      case "expired":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleWithdrawQuote = async (quoteId: string) => {
    try {
      await updateQuote({
        id: quoteId,
        status: "withdrawn",
      }).unwrap();

      toast({
        title: "Quote Withdrawn",
        description: "Your quote has been withdrawn successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to withdraw quote.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteQuote = async (quoteId: string) => {
    try {
      await deleteQuote(quoteId).unwrap();

      toast({
        title: "Quote Deleted",
        description: "Your quote has been deleted successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete quote.",
        variant: "destructive",
      });
    }
  };

  const QuoteCard = ({ quote }: { quote: any }) => {
    const isExpired =
      quote.valid_until && new Date(quote.valid_until) < new Date();
    const canEdit = quote.status === "pending" && !isExpired;

    return (
      <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg font-bold text-slate-900 dark:text-white mb-2">
                {quote.title}
              </CardTitle>
              <CardDescription className="text-slate-600 dark:text-slate-300">
                Project ID: {quote.project_id}
              </CardDescription>
            </div>
            <Badge
              className={`${getStatusColor(
                quote.status
              )} flex items-center gap-1`}
            >
              {getStatusIcon(quote.status)}
              {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Quote Amount */}
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-emerald-600" />
                <span className="font-medium text-slate-700 dark:text-slate-300">
                  Quote Amount
                </span>
              </div>
              <span className="text-xl font-bold text-emerald-600">
                {formatCurrency(quote.total_amount)}
              </span>
            </div>
          </div>

          {/* Timeline */}
          {(quote.estimated_duration || quote.start_date || quote.end_date) && (
            <div className="flex items-center gap-4 p-3 bg-slate-50 dark:bg-slate-700 rounded-lg text-sm">
              <Calendar className="h-4 w-4 text-slate-600" />
              <div className="flex gap-4">
                {quote.estimated_duration && (
                  <span>Duration: {quote.estimated_duration} days</span>
                )}
                {quote.start_date && (
                  <span>
                    Start: {new Date(quote.start_date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Description */}
          {quote.description && (
            <p className="text-sm text-slate-600 dark:text-slate-300 line-clamp-2">
              {quote.description}
            </p>
          )}

          {/* Valid Until */}
          {quote.valid_until && (
            <div className="flex items-center gap-2 text-sm">
              <AlertCircle
                className={`h-4 w-4 ${
                  isExpired ? "text-red-500" : "text-orange-500"
                }`}
              />
              <span className={isExpired ? "text-red-600" : "text-orange-600"}>
                {isExpired ? "Expired on" : "Valid until"}:{" "}
                {new Date(quote.valid_until).toLocaleDateString()}
              </span>
            </div>
          )}

          {/* Actions */}
          <div className="flex flex-wrap gap-2 pt-4 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/contractor/quotes/${quote.id}`)}
            >
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </Button>

            {canEdit && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditingQuoteId(quote.id)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleWithdrawQuote(quote.id)}
                  className="text-orange-600 hover:bg-orange-50"
                >
                  Withdraw
                </Button>
              </>
            )}

            {(quote.status === "rejected" || quote.status === "withdrawn") && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDeleteQuote(quote.id)}
                className="text-red-600 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            )}
          </div>

          {/* Quote Info */}
          <div className="text-xs text-slate-500 dark:text-slate-400 pt-2 border-t">
            Submitted on {new Date(quote.created_at).toLocaleDateString()} at{" "}
            {new Date(quote.created_at).toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/contractor/dashboard")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg">
                  <FileText className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                    Quote Management
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    Manage your project quotes and proposals
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button onClick={() => router.push("/contractor/jobs")}>
                <Search className="h-4 w-4 mr-2" />
                Find Projects
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-yellow-500 to-orange-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100">Pending</p>
                  <p className="text-3xl font-bold">
                    {getStatusCount("pending")}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-yellow-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-100">Accepted</p>
                  <p className="text-3xl font-bold">
                    {getStatusCount("accepted")}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-emerald-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-red-500 to-pink-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100">Rejected</p>
                  <p className="text-3xl font-bold">
                    {getStatusCount("rejected")}
                  </p>
                </div>
                <XCircle className="h-8 w-8 text-red-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-gray-500 to-slate-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-100">Withdrawn</p>
                  <p className="text-3xl font-bold">
                    {getStatusCount("withdrawn")}
                  </p>
                </div>
                <AlertCircle className="h-8 w-8 text-gray-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quotes Tabs */}
        <Tabs defaultValue="pending" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pending" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Pending ({getStatusCount("pending")})
            </TabsTrigger>
            <TabsTrigger value="accepted" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Accepted ({getStatusCount("accepted")})
            </TabsTrigger>
            <TabsTrigger value="rejected" className="flex items-center gap-2">
              <XCircle className="h-4 w-4" />
              Rejected ({getStatusCount("rejected")})
            </TabsTrigger>
            <TabsTrigger value="withdrawn" className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              Withdrawn ({getStatusCount("withdrawn")})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="space-y-6">
            {isLoading ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-6 bg-slate-200 rounded w-3/4"></div>
                      <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="h-4 bg-slate-200 rounded"></div>
                        <div className="h-4 bg-slate-200 rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : pendingQuotes.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {pendingQuotes.map((quote) => (
                  <QuoteCard key={quote.id} quote={quote} />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <Clock className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">
                    No Pending Quotes
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-6">
                    You don't have any pending quotes. Find projects to submit
                    quotes for.
                  </p>
                  <Button onClick={() => router.push("/contractor/jobs")}>
                    <Search className="h-4 w-4 mr-2" />
                    Find Projects
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="accepted" className="space-y-6">
            {acceptedQuotes.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {acceptedQuotes.map((quote) => (
                  <QuoteCard key={quote.id} quote={quote} />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <CheckCircle className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">
                    No Accepted Quotes
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    You don't have any accepted quotes yet.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="rejected" className="space-y-6">
            {rejectedQuotes.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {rejectedQuotes.map((quote) => (
                  <QuoteCard key={quote.id} quote={quote} />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <XCircle className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">
                    No Rejected Quotes
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    You don't have any rejected quotes.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="withdrawn" className="space-y-6">
            {withdrawnQuotes.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {withdrawnQuotes.map((quote) => (
                  <QuoteCard key={quote.id} quote={quote} />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <AlertCircle className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">
                    No Withdrawn Quotes
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    You haven't withdrawn any quotes.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Quote Edit Form Dialog */}
      {editingQuoteId && (
        <QuoteEditForm
          quoteId={editingQuoteId}
          open={!!editingQuoteId}
          onOpenChange={(open) => !open && setEditingQuoteId(null)}
          onSuccess={() => {
            setEditingQuoteId(null);
            toast({
              title: "Quote Updated",
              description: "Your quote has been updated successfully.",
            });
          }}
          onCancel={() => setEditingQuoteId(null)}
        />
      )}
    </div>
  );
}
