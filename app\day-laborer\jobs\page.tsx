"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  MapPin,
  Clock,
  DollarSign,
  Calendar,
  Users,
  ArrowLeft,
  Star,
  Briefcase,
  AlertCircle,
  CheckCircle,
  Eye,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface DayLaborJob {
  id: string;
  title: string;
  description: string;
  client: {
    name: string;
    rating: number;
    reviewsCount: number;
    verified: boolean;
  };
  hourlyRate: number;
  duration: number; // hours
  date: string;
  startTime: string;
  location: string;
  category: string;
  requirements: string[];
  urgency: "low" | "medium" | "high";
  status: "available" | "applied" | "hired";
  postedDate: string;
  applicants: number;
}

// Mock data for day laborer jobs
const mockJobs: DayLaborJob[] = [
  {
    id: "1",
    title: "Moving Help - 2 Bedroom Apartment",
    description: "Need help moving furniture and boxes from a 2-bedroom apartment to a new house. Heavy lifting required. Must be reliable and careful with belongings.",
    client: {
      name: "Sarah Johnson",
      rating: 4.8,
      reviewsCount: 23,
      verified: true,
    },
    hourlyRate: 25,
    duration: 6,
    date: "2024-01-25",
    startTime: "09:00",
    location: "Downtown, San Francisco",
    category: "Moving",
    requirements: ["Physical strength", "Reliable", "Own transportation"],
    urgency: "medium",
    status: "available",
    postedDate: "2024-01-20",
    applicants: 3,
  },
  {
    id: "2",
    title: "Yard Cleanup and Landscaping",
    description: "Large backyard needs cleanup including leaf removal, weeding, and basic landscaping. Tools will be provided.",
    client: {
      name: "Michael Chen",
      rating: 4.9,
      reviewsCount: 45,
      verified: true,
    },
    hourlyRate: 20,
    duration: 8,
    date: "2024-01-26",
    startTime: "08:00",
    location: "Suburbs, San Francisco",
    category: "Landscaping",
    requirements: ["Outdoor work experience", "Physical fitness"],
    urgency: "low",
    status: "available",
    postedDate: "2024-01-19",
    applicants: 7,
  },
  {
    id: "3",
    title: "Furniture Assembly - IKEA Items",
    description: "Need help assembling multiple IKEA furniture pieces including bed, dresser, and desk. Experience with furniture assembly preferred.",
    client: {
      name: "Lisa Davis",
      rating: 4.7,
      reviewsCount: 12,
      verified: false,
    },
    hourlyRate: 30,
    duration: 4,
    date: "2024-01-24",
    startTime: "14:00",
    location: "North Side, San Francisco",
    category: "Assembly",
    requirements: ["Furniture assembly experience", "Own tools preferred"],
    urgency: "high",
    status: "applied",
    postedDate: "2024-01-21",
    applicants: 2,
  },
  {
    id: "4",
    title: "Event Setup and Cleanup",
    description: "Help with setting up tables, chairs, and decorations for a birthday party. Cleanup required after event.",
    client: {
      name: "Robert Wilson",
      rating: 4.6,
      reviewsCount: 8,
      verified: true,
    },
    hourlyRate: 22,
    duration: 5,
    date: "2024-01-27",
    startTime: "10:00",
    location: "Mission District, San Francisco",
    category: "Events",
    requirements: ["Attention to detail", "Friendly demeanor"],
    urgency: "medium",
    status: "available",
    postedDate: "2024-01-22",
    applicants: 5,
  },
];

export default function DayLaborerJobsPage() {
  const router = useRouter();
  const { user, selectedRoles, isAuthenticated, profileCompletionStatus } = useSelector((state: RootState) => state.auth);
  const [jobs, setJobs] = useState<DayLaborJob[]>(mockJobs);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedUrgency, setSelectedUrgency] = useState("all");
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState("available");

  // Check if user is a day laborer and profile completion status
  const isDayLaborer = selectedRoles?.includes("day_laborer");
  const dayLaborerProfileComplete = profileCompletionStatus?.day_laborer?.completed || false;
  const dayLaborerCompletionPercentage = profileCompletionStatus?.day_laborer?.percentage || 0;

  // Redirect non-day laborers to role selection
  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!isDayLaborer) {
      router.push("/auth/role-selection?redirect=/day-laborer/jobs");
      return;
    }
  }, [isAuthenticated, isDayLaborer, router]);

  const categories = ["all", "Moving", "Landscaping", "Assembly", "Events", "Cleaning", "Delivery"];
  const urgencyLevels = ["all", "low", "medium", "high"];

  const filteredJobs = jobs.filter((job) => {
    const matchesSearch = job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === "all" || job.category === selectedCategory;
    const matchesUrgency = selectedUrgency === "all" || job.urgency === selectedUrgency;
    const matchesTab = activeTab === "all" || job.status === activeTab;

    return matchesSearch && matchesCategory && matchesUrgency && matchesTab;
  });

  const availableJobs = jobs.filter(job => job.status === "available");
  const appliedJobs = jobs.filter(job => job.status === "applied");
  const hiredJobs = jobs.filter(job => job.status === "hired");

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "applied":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "hired":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const handleApplyToJob = (jobId: string) => {
    if (!dayLaborerProfileComplete) {
      router.push("/profile/complete?role=day_laborer");
      return;
    }

    // Update job status to applied
    setJobs(prev => prev.map(job => 
      job.id === jobId ? { ...job, status: "applied" as const, applicants: job.applicants + 1 } : job
    ));
  };

  if (!isAuthenticated || !isDayLaborer) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-orange-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/day-laborer/dashboard")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-lg">
                  <Briefcase className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">Find Work</h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">Discover day labor opportunities</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Completion Warning */}
        {!dayLaborerProfileComplete && (
          <Card className="mb-8 border-orange-200 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <AlertCircle className="h-6 w-6 text-orange-600 mt-1" />
                <div className="flex-1">
                  <h3 className="font-semibold text-orange-800 dark:text-orange-200 mb-2">
                    Complete Your Profile to Apply for Jobs
                  </h3>
                  <p className="text-orange-700 dark:text-orange-300 mb-4">
                    Your profile is {dayLaborerCompletionPercentage}% complete. Complete it to start applying for day labor jobs.
                  </p>
                  <Button 
                    onClick={() => router.push("/profile/complete?role=day_laborer")}
                    className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  >
                    Complete Profile
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
              <Input
                placeholder="Search jobs by title, description, or category..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="h-12 px-6"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>

          {/* Filter Options */}
          {showFilters && (
            <Card className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Category</label>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category === "all" ? "All Categories" : category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Urgency</label>
                  <Select value={selectedUrgency} onValueChange={setSelectedUrgency}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select urgency" />
                    </SelectTrigger>
                    <SelectContent>
                      {urgencyLevels.map((urgency) => (
                        <SelectItem key={urgency} value={urgency}>
                          {urgency === "all" ? "All Urgency Levels" : urgency.charAt(0).toUpperCase() + urgency.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setSelectedCategory("all");
                      setSelectedUrgency("all");
                      setSearchQuery("");
                    }}
                    className="w-full"
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Job Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="available" className="flex items-center gap-2">
              <Briefcase className="h-4 w-4" />
              Available ({availableJobs.length})
            </TabsTrigger>
            <TabsTrigger value="applied" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Applied ({appliedJobs.length})
            </TabsTrigger>
            <TabsTrigger value="hired" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Hired ({hiredJobs.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-6">
            {filteredJobs.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {filteredJobs.map((job) => (
                  <JobCard 
                    key={job.id} 
                    job={job} 
                    onApply={handleApplyToJob}
                    canApply={dayLaborerProfileComplete}
                  />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <Briefcase className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">No Jobs Found</h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-6">
                    {activeTab === "available" 
                      ? "No available jobs match your search criteria. Try adjusting your filters."
                      : activeTab === "applied"
                      ? "You haven't applied to any jobs yet."
                      : "You haven't been hired for any jobs yet."
                    }
                  </p>
                  {activeTab === "available" && (
                    <Button 
                      onClick={() => {
                        setSelectedCategory("all");
                        setSelectedUrgency("all");
                        setSearchQuery("");
                      }}
                    >
                      Clear Filters
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

interface JobCardProps {
  job: DayLaborJob;
  onApply: (jobId: string) => void;
  canApply: boolean;
}

function JobCard({ job, onApply, canApply }: JobCardProps) {
  const router = useRouter();
  
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "applied":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "hired":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  return (
    <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-bold text-slate-900 dark:text-white mb-2">
              {job.title}
            </CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-300">
              {job.client.name} • {job.location}
            </CardDescription>
          </div>
          <div className="flex flex-col gap-2">
            <Badge className={`${getStatusColor(job.status)} flex items-center gap-1`}>
              {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
            </Badge>
            <Badge className={`${getUrgencyColor(job.urgency)} flex items-center gap-1`}>
              {job.urgency.charAt(0).toUpperCase() + job.urgency.slice(1)} Priority
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Job Details */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-4 rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-orange-600" />
              <span className="font-medium">{formatCurrency(job.hourlyRate)}/hour</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-600" />
              <span>{job.duration} hours</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-orange-600" />
              <span>{new Date(job.date).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-orange-600" />
              <span>{job.applicants} applicants</span>
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-sm text-slate-600 dark:text-slate-300 line-clamp-3">
          {job.description}
        </p>

        {/* Client Info */}
        <div className="flex items-center gap-4 p-3 bg-slate-50 dark:bg-slate-700 rounded-lg text-sm">
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4 text-yellow-500 fill-current" />
            <span>{job.client.rating}</span>
            <span className="text-slate-500">({job.client.reviewsCount} reviews)</span>
          </div>
          {job.client.verified && (
            <Badge variant="outline" className="text-xs">
              <CheckCircle className="h-3 w-3 mr-1" />
              Verified
            </Badge>
          )}
        </div>

        {/* Requirements */}
        {job.requirements.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-slate-900 dark:text-white">Requirements:</h4>
            <div className="flex flex-wrap gap-2">
              {job.requirements.map((req, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {req}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col gap-3 pt-4 border-t">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => router.push(`/day-laborer/jobs/${job.id}`)}
              className="flex-1"
            >
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </Button>
            
            {job.status === "available" && (
              <Button
                onClick={() => onApply(job.id)}
                disabled={!canApply}
                className="flex-1 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
              >
                {canApply ? "Apply Now" : "Complete Profile"}
              </Button>
            )}
          </div>

          {/* Job Info */}
          <div className="text-xs text-slate-500 dark:text-slate-400">
            Posted on {new Date(job.postedDate).toLocaleDateString()} • 
            Total earnings: {formatCurrency(job.hourlyRate * job.duration)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
