"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  MapPin,
  DollarSign,
  Clock,
  Star,
  Bookmark,
  BookmarkCheck,
  Filter,
  Briefcase,
  TrendingUp,
  Users,
  Award,
  AlertCircle,
  CheckCircle,
  ArrowRight,
  Eye,
  Calendar,
} from "lucide-react";
import { ProfileCompletionPrompt } from "@/components/contractor/profile-completion-prompt";
import { QuoteCreationForm } from "@/components/quotes/quote-creation-form";

interface Job {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  budget: {
    min: number;
    max: number;
    type: "fixed" | "hourly";
  };
  timeline: string;
  postedDate: string;
  applicants: number;
  homeowner: {
    name: string;
    rating: number;
    reviewsCount: number;
    verified: boolean;
  };
  requirements: string[];
  tags: string[];
  urgency: "low" | "medium" | "high";
  saved: boolean;
}

const mockJobs: Job[] = [
  {
    id: "1",
    title: "Kitchen Renovation - Modern Design",
    description:
      "Looking for an experienced contractor to renovate our 200 sq ft kitchen. Need complete overhaul including cabinets, countertops, flooring, and electrical work.",
    category: "Kitchen Renovation",
    location: "San Francisco, CA",
    budget: { min: 15000, max: 25000, type: "fixed" },
    timeline: "6-8 weeks",
    postedDate: "2024-01-15",
    applicants: 12,
    homeowner: {
      name: "Sarah Johnson",
      rating: 4.8,
      reviewsCount: 23,
      verified: true,
    },
    requirements: [
      "Licensed contractor",
      "5+ years experience",
      "Portfolio required",
    ],
    tags: ["Kitchen", "Renovation", "Modern", "High-end"],
    urgency: "medium",
    saved: false,
  },
  {
    id: "2",
    title: "Bathroom Remodel - Master Suite",
    description:
      "Complete master bathroom renovation including tile work, plumbing, and fixtures. Looking for quality craftsmanship and attention to detail.",
    category: "Bathroom Renovation",
    location: "Los Angeles, CA",
    budget: { min: 8000, max: 12000, type: "fixed" },
    timeline: "4-5 weeks",
    postedDate: "2024-01-14",
    applicants: 8,
    homeowner: {
      name: "Michael Chen",
      rating: 4.9,
      reviewsCount: 15,
      verified: true,
    },
    requirements: [
      "Licensed plumber",
      "Tile experience",
      "References required",
    ],
    tags: ["Bathroom", "Tile", "Plumbing", "Luxury"],
    urgency: "high",
    saved: true,
  },
];

export default function ContractorJobsPage() {
  const router = useRouter();
  const { user, selectedRoles, profileCompletionStatus } = useSelector(
    (state: RootState) => state.auth
  );

  const [jobs, setJobs] = useState<Job[]>(mockJobs);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [showFilters, setShowFilters] = useState(false);
  const [savedJobsOnly, setSavedJobsOnly] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [showQuoteForm, setShowQuoteForm] = useState(false);

  // Check if user is a contractor and profile completion status
  const isContractor = selectedRoles?.includes("contractor");
  const contractorProfileComplete =
    profileCompletionStatus?.contractor?.completed || false;
  const contractorCompletionPercentage =
    profileCompletionStatus?.contractor?.percentage || 0;

  // Redirect non-contractors to role selection
  useEffect(() => {
    if (!isContractor) {
      router.push("/auth/role-selection?redirect=/contractor/jobs");
      return;
    }
  }, [isContractor, router]);

  const handleSaveJob = (jobId: string) => {
    setJobs((prev) =>
      prev.map((job) =>
        job.id === jobId ? { ...job, saved: !job.saved } : job
      )
    );
  };

  const handleApplyToJob = (jobId: string) => {
    if (!contractorProfileComplete) {
      router.push("/profile/complete?role=contractor");
      return;
    }

    const job = jobs.find((j) => j.id === jobId);
    if (job) {
      setSelectedJob(job);
      setShowQuoteForm(true);
    }
  };

  const filteredJobs = jobs.filter((job) => {
    const matchesSearch =
      job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" || job.category === selectedCategory;
    const matchesSaved = !savedJobsOnly || job.saved;

    return matchesSearch && matchesCategory && matchesSaved;
  });

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (!isContractor) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg">
                  <Briefcase className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                    Find Work
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    Discover your next project
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button
                variant={savedJobsOnly ? "default" : "outline"}
                onClick={() => setSavedJobsOnly(!savedJobsOnly)}
                className="hidden sm:flex"
              >
                <Bookmark className="h-4 w-4 mr-2" />
                Saved Jobs
              </Button>
              <Button
                onClick={() => router.push("/contractor/services/create")}
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Offer Services
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Completion Prompt */}
        {!contractorProfileComplete && (
          <ProfileCompletionPrompt
            role="contractor"
            completionPercentage={contractorCompletionPercentage}
            onComplete={() => router.push("/profile/complete?role=contractor")}
            benefits={[
              "Apply to premium job listings",
              "Submit detailed quotes with portfolio",
              "Higher visibility in search results",
              "Access to exclusive contractor features",
            ]}
          />
        )}

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
              <Input
                placeholder="Search jobs by title, description, or category..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="h-12 px-6"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>

          {showFilters && (
            <Card className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Category
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full p-2 border rounded-lg"
                  >
                    <option value="all">All Categories</option>
                    <option value="Kitchen Renovation">
                      Kitchen Renovation
                    </option>
                    <option value="Bathroom Renovation">
                      Bathroom Renovation
                    </option>
                    <option value="Roofing">Roofing</option>
                    <option value="Plumbing">Plumbing</option>
                    <option value="Electrical">Electrical</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Budget Range
                  </label>
                  <select className="w-full p-2 border rounded-lg">
                    <option value="all">All Budgets</option>
                    <option value="0-5000">$0 - $5,000</option>
                    <option value="5000-15000">$5,000 - $15,000</option>
                    <option value="15000-50000">$15,000 - $50,000</option>
                    <option value="50000+">$50,000+</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Location
                  </label>
                  <Input placeholder="Enter city or zip code" />
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-100">Available Jobs</p>
                  <p className="text-3xl font-bold">{filteredJobs.length}</p>
                </div>
                <Briefcase className="h-8 w-8 text-emerald-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Saved Jobs</p>
                  <p className="text-3xl font-bold">
                    {jobs.filter((j) => j.saved).length}
                  </p>
                </div>
                <Bookmark className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-red-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Applications</p>
                  <p className="text-3xl font-bold">5</p>
                </div>
                <Users className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-pink-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Success Rate</p>
                  <p className="text-3xl font-bold">78%</p>
                </div>
                <Award className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Job Listings */}
        <div className="space-y-6">
          {filteredJobs.map((job) => (
            <Card
              key={job.id}
              className="hover:shadow-lg transition-all duration-300 border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm"
            >
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                            {job.title}
                          </h3>
                          <Badge
                            className={`text-xs ${getUrgencyColor(
                              job.urgency
                            )}`}
                          >
                            {job.urgency} priority
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-300 mb-3">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {job.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />$
                            {job.budget.min.toLocaleString()} - $
                            {job.budget.max.toLocaleString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {job.timeline}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSaveJob(job.id)}
                        className="text-slate-500 hover:text-emerald-600"
                      >
                        {job.saved ? (
                          <BookmarkCheck className="h-5 w-5" />
                        ) : (
                          <Bookmark className="h-5 w-5" />
                        )}
                      </Button>
                    </div>

                    <p className="text-slate-700 dark:text-slate-300 mb-4 line-clamp-2">
                      {job.description}
                    </p>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {job.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-300">
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full flex items-center justify-center text-white text-xs font-medium">
                            {job.homeowner.name.charAt(0)}
                          </div>
                          <div>
                            <p className="font-medium">{job.homeowner.name}</p>
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span>{job.homeowner.rating}</span>
                              <span>
                                ({job.homeowner.reviewsCount} reviews)
                              </span>
                              {job.homeowner.verified && (
                                <CheckCircle className="h-3 w-3 text-emerald-500" />
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          {job.applicants} applicants
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Posted {new Date(job.postedDate).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col gap-3 lg:w-48">
                    <Button
                      onClick={() => handleApplyToJob(job.id)}
                      className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
                      disabled={!contractorProfileComplete}
                    >
                      {contractorProfileComplete ? (
                        <>
                          Apply Now
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-4 w-4 mr-2" />
                          Complete Profile
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => router.push(`/contractor/jobs/${job.id}`)}
                      className="w-full"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredJobs.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Briefcase className="h-16 w-16 mx-auto text-slate-400 mb-4" />
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                No jobs found
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                Try adjusting your search criteria or check back later for new
                opportunities.
              </p>
              <Button onClick={() => setSearchQuery("")}>Clear Search</Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Quote Creation Dialog */}
      {selectedJob && (
        <QuoteCreationForm
          projectId={selectedJob.id}
          homeownerId="mock-homeowner-id" // In real app, this would come from the job data
          projectTitle={selectedJob.title}
          open={showQuoteForm}
          onOpenChange={setShowQuoteForm}
          onSuccess={() => {
            setShowQuoteForm(false);
            setSelectedJob(null);
            router.push("/contractor/quotes");
          }}
          onCancel={() => {
            setShowQuoteForm(false);
            setSelectedJob(null);
          }}
        />
      )}
    </div>
  );
}
