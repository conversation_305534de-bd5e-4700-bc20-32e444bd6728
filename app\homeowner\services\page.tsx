"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import {
  useGetServiceListingsQuery,
  ServiceListing,
  ServiceListingFilters,
} from "@/lib/store/api/serviceListingsApi";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Users,
  ArrowLeft,
  Briefcase,
  AlertCircle,
  CheckCircle,
  Eye,
  ShoppingCart,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

const serviceCategories = [
  "All Categories",
  "Plumbing",
  "Electrical",
  "HVAC",
  "Carpentry",
  "Painting",
  "Roofing",
  "Flooring",
  "Landscaping",
  "Cleaning",
  "Moving",
  "Handyman",
  "Other",
];

const sortOptions = [
  { value: "rating", label: "Highest Rated" },
  { value: "price", label: "Lowest Price" },
  { value: "order_count", label: "Most Popular" },
  { value: "created_at", label: "Newest" },
];

export default function HomeownerServicesPage() {
  const router = useRouter();
  const { user, selectedRoles, isAuthenticated, profileCompletionStatus } = useSelector((state: RootState) => state.auth);
  
  const [filters, setFilters] = useState<ServiceListingFilters>({
    sort_by: "rating",
    sort_order: "desc",
    limit: 20,
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [showFilters, setShowFilters] = useState(false);

  const { data: listings = [], isLoading, error } = useGetServiceListingsQuery(filters);

  // Check if user is a homeowner and profile completion status
  const isHomeowner = selectedRoles?.includes("homeowner");
  const homeownerProfileComplete = profileCompletionStatus?.homeowner?.completed || false;
  const homeownerCompletionPercentage = profileCompletionStatus?.homeowner?.percentage || 0;

  // Redirect non-homeowners to role selection
  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!isHomeowner) {
      router.push("/auth/role-selection?redirect=/homeowner/services");
      return;
    }
  }, [isAuthenticated, isHomeowner, router]);

  // Update filters when search or category changes
  useEffect(() => {
    const newFilters: ServiceListingFilters = {
      ...filters,
      search: searchQuery || undefined,
      category: selectedCategory === "All Categories" ? undefined : selectedCategory,
      price_min: priceRange.min ? parseFloat(priceRange.min) : undefined,
      price_max: priceRange.max ? parseFloat(priceRange.max) : undefined,
    };
    setFilters(newFilters);
  }, [searchQuery, selectedCategory, priceRange]);

  const handleSortChange = (sortBy: string) => {
    setFilters(prev => ({
      ...prev,
      sort_by: sortBy as any,
      sort_order: sortBy === "price" ? "asc" : "desc",
    }));
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedCategory("All Categories");
    setPriceRange({ min: "", max: "" });
    setFilters({
      sort_by: "rating",
      sort_order: "desc",
      limit: 20,
    });
  };

  const ServiceCard = ({ listing }: { listing: ServiceListing }) => {
    return (
      <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg font-bold text-slate-900 dark:text-white mb-2">
                {listing.title}
              </CardTitle>
              <CardDescription className="text-slate-600 dark:text-slate-300">
                {listing.category} {listing.subcategory && `• ${listing.subcategory}`}
              </CardDescription>
            </div>
            {listing.featured && (
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                Featured
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Pricing */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-slate-700 dark:text-slate-300">
                  {listing.price_type === "fixed" ? "Fixed Price" : 
                   listing.price_type === "hourly" ? "Per Hour" :
                   listing.price_type === "per_sqft" ? "Per Sq Ft" : "Per Item"}
                </span>
              </div>
              <div className="text-right">
                <span className="text-xl font-bold text-blue-600">
                  {formatCurrency(listing.base_price)}
                </span>
                {listing.min_price && listing.max_price && (
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Range: {formatCurrency(listing.min_price)} - {formatCurrency(listing.max_price)}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                <span className="font-semibold">{listing.rating.toFixed(1)}</span>
              </div>
              <p className="text-xs text-slate-600 dark:text-slate-400">Rating</p>
            </div>
            <div className="p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Users className="h-4 w-4 text-blue-500" />
                <span className="font-semibold">{listing.review_count}</span>
              </div>
              <p className="text-xs text-slate-600 dark:text-slate-400">Reviews</p>
            </div>
            <div className="p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="font-semibold">{listing.order_count}</span>
              </div>
              <p className="text-xs text-slate-600 dark:text-slate-400">Completed</p>
            </div>
          </div>

          {/* Description */}
          {listing.description && (
            <p className="text-sm text-slate-600 dark:text-slate-300 line-clamp-3">
              {listing.description}
            </p>
          )}

          {/* Duration */}
          {listing.duration_estimate && (
            <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300">
              <Clock className="h-4 w-4" />
              <span>Duration: {listing.duration_estimate}</span>
            </div>
          )}

          {/* Service Area */}
          {listing.service_area && listing.service_area.length > 0 && (
            <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300">
              <MapPin className="h-4 w-4" />
              <span>Serves: {listing.service_area.slice(0, 2).join(", ")}</span>
              {listing.service_area.length > 2 && (
                <span className="text-slate-500">+{listing.service_area.length - 2} more</span>
              )}
            </div>
          )}

          {/* Tags */}
          {listing.tags && listing.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {listing.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {listing.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{listing.tags.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => router.push(`/homeowner/services/${listing.id}`)}
              className="flex-1"
            >
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </Button>
            
            <Button
              onClick={() => router.push(`/homeowner/services/${listing.id}/order`)}
              disabled={!homeownerProfileComplete}
              className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {homeownerProfileComplete ? "Order Now" : "Complete Profile"}
            </Button>
          </div>

          {/* Listing Info */}
          <div className="text-xs text-slate-500 dark:text-slate-400">
            Listed on {new Date(listing.created_at).toLocaleDateString()}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (!isAuthenticated || !isHomeowner) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-blue-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/homeowner/dashboard")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg">
                  <Briefcase className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">Browse Services</h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">Find professional services for your home</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Completion Warning */}
        {!homeownerProfileComplete && (
          <Card className="mb-8 border-orange-200 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <AlertCircle className="h-6 w-6 text-orange-600 mt-1" />
                <div className="flex-1">
                  <h3 className="font-semibold text-orange-800 dark:text-orange-200 mb-2">
                    Complete Your Profile to Order Services
                  </h3>
                  <p className="text-orange-700 dark:text-orange-300 mb-4">
                    Your profile is {homeownerCompletionPercentage}% complete. Complete it to start ordering services.
                  </p>
                  <Button 
                    onClick={() => router.push("/profile/complete?role=homeowner")}
                    className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  >
                    Complete Profile
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
              <Input
                placeholder="Search services by title, description, or category..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="h-12 px-6"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>

          {/* Filter Options */}
          {showFilters && (
            <Card className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Category</label>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {serviceCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Min Price</label>
                  <Input
                    type="number"
                    placeholder="0"
                    value={priceRange.min}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Price</label>
                  <Input
                    type="number"
                    placeholder="1000"
                    value={priceRange.max}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Sort By</label>
                  <Select 
                    value={filters.sort_by} 
                    onValueChange={handleSortChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      {sortOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end mt-4">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
              </div>
            </Card>
          )}
        </div>

        {/* Results */}
        <div className="mb-6">
          <p className="text-slate-600 dark:text-slate-300">
            {isLoading ? "Loading..." : `${listings.length} services found`}
          </p>
        </div>

        {/* Services Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-slate-200 rounded w-3/4"></div>
                  <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="h-20 bg-slate-200 rounded"></div>
                    <div className="h-16 bg-slate-200 rounded"></div>
                    <div className="h-10 bg-slate-200 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : listings.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {listings.map((listing) => (
              <ServiceCard key={listing.id} listing={listing} />
            ))}
          </div>
        ) : (
          <Card className="text-center py-12">
            <CardContent>
              <Briefcase className="h-16 w-16 mx-auto mb-4 text-slate-400" />
              <h3 className="text-xl font-semibold mb-2">No Services Found</h3>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                No services match your search criteria. Try adjusting your filters or search terms.
              </p>
              <Button onClick={clearFilters}>
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
